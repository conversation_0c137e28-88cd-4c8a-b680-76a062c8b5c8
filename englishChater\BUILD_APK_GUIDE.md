# Android APK 构建指南

本指南提供多种方法将您的英语学习应用构建为Android APK文件。

## 方法一：Expo Build APK（推荐 - 最简单）

### 优点
- 无需EAS账户
- 最简单快捷
- 适合个人使用

### 步骤
1. **确保已安装Expo CLI**：
   ```bash
   npm install -g @expo/cli
   ```

2. **进入项目目录**：
   ```bash
   cd englishChater
   ```

3. **构建APK**：
   ```bash
   expo build:android -t apk
   ```

4. **等待构建完成**：
   - 构建过程在Expo服务器上进行
   - 通常需要10-20分钟
   - 完成后会提供下载链接

5. **下载APK**：
   - 点击提供的链接下载APK文件
   - 文件名类似：`app-release.apk`

## 方法二：EAS Build（免费层）

### 优点
- 更现代的构建系统
- 更好的配置选项
- 免费层足够个人使用

### 步骤
1. **安装EAS CLI**：
   ```bash
   npm install -g eas-cli
   ```

2. **创建Expo账户（免费）**：
   ```bash
   eas login
   ```
   - 如果没有账户，会引导您创建

3. **配置项目**：
   ```bash
   eas build:configure
   ```

4. **构建预览版APK**：
   ```bash
   eas build --platform android --profile preview
   ```

5. **下载APK**：
   - 构建完成后，在EAS控制台下载APK

## 方法三：本地构建（高级用户）

### 前提条件
- 安装Android Studio
- 配置Android SDK
- 设置环境变量

### 步骤
1. **预构建项目**：
   ```bash
   npx expo prebuild --platform android
   ```

2. **进入Android目录**：
   ```bash
   cd android
   ```

3. **构建发布版APK**：
   ```bash
   ./gradlew assembleRelease
   ```

4. **查找APK文件**：
   ```
   android/app/build/outputs/apk/release/app-release.apk
   ```

## 方法四：使用GitHub Actions（自动化）

创建 `.github/workflows/build.yml` 文件实现自动构建。

## 构建配置说明

### app.json 重要配置
- `android.package`: 应用包名
- `android.versionCode`: 版本号
- `android.permissions`: 所需权限

### 当前配置
- 包名：`com.englishpractice.app`
- 版本：1.0.0
- 权限：录音、网络访问

## 安装APK

1. **启用未知来源**：
   - 设置 > 安全 > 未知来源
   - 或设置 > 应用和通知 > 特殊应用访问 > 安装未知应用

2. **安装APK**：
   - 将APK文件传输到Android设备
   - 点击文件进行安装

## 故障排除

### 常见问题
1. **构建失败**：检查网络连接和依赖项
2. **安装失败**：确保启用了未知来源
3. **权限问题**：检查Android权限配置

### 调试技巧
- 使用 `expo doctor` 检查项目健康状态
- 查看构建日志了解错误详情
- 确保所有依赖项都是兼容版本

## 推荐流程

对于您的情况，推荐按以下顺序尝试：

1. **首选**：方法一（Expo Build APK）
2. **备选**：方法二（EAS Build免费层）
3. **高级**：方法三（本地构建）

## 注意事项

- 构建过程需要网络连接
- 首次构建可能需要更长时间
- 保存好APK文件以备后续使用
- 定期更新Expo CLI以获得最新功能
