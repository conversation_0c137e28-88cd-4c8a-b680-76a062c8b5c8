{"name": "english-speaking-practice", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint", "build:android": "expo build:android -t apk", "build:eas": "eas build --platform android --profile preview", "build:local": "npx expo prebuild --platform android && cd android && ./gradlew assembleRelease", "build:apk": "node ./scripts/build-apk.js", "build:apk:clean": "node ./scripts/build-apk.js --clean"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@react-navigation/stack": "^7.3.2", "axios": "^1.9.0", "expo": "~53.0.7", "expo-av": "^15.1.4", "expo-blur": "~14.1.4", "expo-constants": "~17.1.5", "expo-crypto": "^14.1.4", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.6", "expo-linking": "~7.1.4", "expo-router": "~5.0.5", "expo-speech": "~13.1.6", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.10.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/cli": "^18.0.0", "@types/react": "~19.0.10", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}