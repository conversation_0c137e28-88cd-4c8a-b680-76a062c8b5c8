# 🚀 快速构建 APK 指南

## 最简单的方法（推荐）

### Windows 用户
1. 双击运行 `build-apk.bat`
2. 选择选项 1（Expo Build APK）
3. 等待构建完成（10-20分钟）
4. 下载生成的APK文件

### 命令行用户
```bash
# 进入项目目录
cd englishChater

# 方法1：使用我们的构建脚本
npm run build:apk

# 方法2：直接使用Expo命令
npm run build:android

# 方法3：使用EAS Build（需要账户）
npm run build:eas
```

## 一键构建命令

```bash
# 最简单的构建命令
npm run build:apk

# 带清理的构建（如果遇到问题）
npm run build:apk:clean
```

## 构建完成后

1. **下载APK**：点击构建完成后提供的下载链接
2. **传输到手机**：通过USB、云盘或其他方式
3. **安装APK**：
   - 启用"未知来源"安装
   - 点击APK文件进行安装

## 故障排除

### 如果构建失败
```bash
# 检查项目状态
expo doctor

# 清理并重新构建
npm run build:apk:clean

# 尝试EAS Build
npm run build:eas
```

### 常见错误解决
- **网络错误**：检查网络连接，重试构建
- **依赖问题**：运行 `npm install` 重新安装依赖
- **Expo CLI问题**：运行 `npm install -g @expo/cli` 更新

## 文件位置

构建完成后，APK文件通常命名为：
- `app-release.apk`
- `english-speaking-practice.apk`

## 需要帮助？

查看详细指南：`BUILD_APK_GUIDE.md`
