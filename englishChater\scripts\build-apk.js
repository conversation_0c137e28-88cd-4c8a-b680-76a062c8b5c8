#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 英语学习应用 APK 构建脚本');
console.log('================================');

// 检查是否安装了必要的工具
function checkPrerequisites() {
  console.log('📋 检查构建环境...');
  
  try {
    execSync('expo --version', { stdio: 'ignore' });
    console.log('✅ Expo CLI 已安装');
  } catch (error) {
    console.log('❌ Expo CLI 未安装');
    console.log('请运行: npm install -g @expo/cli');
    process.exit(1);
  }
}

// 清理项目
function cleanProject() {
  console.log('🧹 清理项目...');
  
  const dirsToClean = ['node_modules/.cache', '.expo'];
  
  dirsToClean.forEach(dir => {
    const fullPath = path.join(process.cwd(), dir);
    if (fs.existsSync(fullPath)) {
      fs.rmSync(fullPath, { recursive: true, force: true });
      console.log(`✅ 已清理: ${dir}`);
    }
  });
}

// 构建APK
function buildAPK() {
  console.log('🔨 开始构建 APK...');
  console.log('这可能需要 10-20 分钟，请耐心等待...');
  
  try {
    execSync('expo build:android -t apk', { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    console.log('🎉 APK 构建完成！');
    console.log('请查看上方的下载链接获取您的 APK 文件。');
    
  } catch (error) {
    console.error('❌ 构建失败:', error.message);
    console.log('💡 尝试解决方案:');
    console.log('1. 检查网络连接');
    console.log('2. 运行 expo doctor 检查项目');
    console.log('3. 尝试使用 EAS Build: npm run build:eas');
    process.exit(1);
  }
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log('用法:');
    console.log('  node scripts/build-apk.js [选项]');
    console.log('');
    console.log('选项:');
    console.log('  --clean    构建前清理项目');
    console.log('  --help     显示帮助信息');
    return;
  }
  
  checkPrerequisites();
  
  if (args.includes('--clean')) {
    cleanProject();
  }
  
  buildAPK();
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { checkPrerequisites, cleanProject, buildAPK };
