@echo off
echo 🚀 英语学习应用 APK 构建工具
echo ================================
echo.

echo 请选择构建方法:
echo 1. Expo Build APK (推荐 - 最简单)
echo 2. EAS Build (需要账户)
echo 3. 本地构建 (需要Android Studio)
echo 4. 退出
echo.

set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" goto expo_build
if "%choice%"=="2" goto eas_build
if "%choice%"=="3" goto local_build
if "%choice%"=="4" goto exit
goto invalid

:expo_build
echo.
echo 🔨 使用 Expo Build 构建 APK...
echo 这可能需要 10-20 分钟，请耐心等待...
echo.
npm run build:android
goto end

:eas_build
echo.
echo 🔨 使用 EAS Build 构建 APK...
echo 如果没有账户，系统会引导您创建...
echo.
npm run build:eas
goto end

:local_build
echo.
echo 🔨 本地构建 APK...
echo 确保已安装 Android Studio 和 SDK...
echo.
npm run build:local
goto end

:invalid
echo.
echo ❌ 无效选择，请重新运行脚本
pause
goto exit

:end
echo.
echo ✅ 构建完成！
echo 请查看上方的输出信息获取 APK 文件位置。
echo.
pause

:exit
